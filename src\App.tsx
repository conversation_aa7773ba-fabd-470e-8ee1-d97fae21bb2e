import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { scroller } from "react-scroll";
import CallToAction from "./components/CallToAction";
import ContactForm from "./components/ContactForm";
import FAQ from "./components/FAQ";
import Footer from "./components/Footer";
import Header from "./components/Header";
import Hero from "./components/Hero";
import Process from "./components/Process";
import SelfAssessment from "./components/SelfAssessment";
import Services from "./components/Services";
import WhyChooseUs from "./components/WhyChooseUs";
import WhyNow from "./components/WhyNow";
import PrivacyPolicy from "./components/PrivacyPolicy";
import TermsAndConditions from "./components/TermsAndConditions";
import AnimatedBackground from "./components/AnimatedBackground";
import TopLayerParticles from "./components/TopLayerParticles";
import "./index.css";
import AnimatedLogo from "./components/AnimatedLogo";
import Logo2Full from "./components/LOGO Full Purple.svg?react";
const LOADING_LOGO_COLOR = "#fff";

type PageType = 'home' | 'privacy' | 'terms';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showParticles, setShowParticles] = useState(false);
  const [currentPage, setCurrentPage] = useState<PageType>('home');
  const [shouldScrollToContact, setShouldScrollToContact] = useState(false);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Enable particles after loading is complete and page has settled
      setTimeout(() => {
        setShowParticles(true);
      }, 300);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Handle scrolling to contact section after page renders
  useEffect(() => {
    if (shouldScrollToContact && currentPage === 'home') {
      // Use requestAnimationFrame to ensure DOM is fully rendered
      requestAnimationFrame(() => {
        const contactSection = document.getElementById('contact');
        if (contactSection) {
          // Scroll immediately without smooth behavior to avoid glitch
          contactSection.scrollIntoView({
            behavior: 'auto',
            block: 'start'
          });
        }
        setShouldScrollToContact(false);
      });
    }
  }, [currentPage, shouldScrollToContact]);

  const handlePageChange = (page: PageType) => {
    if (page === 'privacy' || page === 'terms') {
      // Navigate to legal pages and scroll to top
      setCurrentPage(page);
      window.scrollTo(0, 0);
    } else if (page === 'home') {
      // Returning to home - flag to scroll to contact section
      setShouldScrollToContact(true);
      setCurrentPage(page);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-900 relative">
        <AnimatedBackground enableParticles={false} />
        <div className="text-center relative z-10">
          <span className="flex justify-center">
            <Logo2Full
              style={{ height: "3.5rem", width: "auto", display: "block" }}
            />
          </span>
          <p className="mt-2 text-white">Loading your experience...</p>
        </div>
      </div>
    );
  }

  // Render legal pages
  if (currentPage === 'privacy') {
    return <PrivacyPolicy onBack={() => handlePageChange('home')} />;
  }

  if (currentPage === 'terms') {
    return <TermsAndConditions onBack={() => handlePageChange('home')} />;
  }

  // Render main home page
  return (
    <div className="min-h-screen text-white relative">
      {/* Animated Background */}
      <AnimatedBackground enableParticles={showParticles} />

      {/* Top Layer Particles - only show on home page after loading */}
      <TopLayerParticles enabled={showParticles && currentPage === 'home'} />

      {/* Main Content */}
      <Header />
      <motion.main
        layout
        transition={{ duration: 0.5, ease: [0.4, 0.0, 0.2, 1] }}
      >
        <Hero />
        <Services />
        <WhyChooseUs />
        <Process />
        <WhyNow />
        <CallToAction />
        <FAQ />
        <SelfAssessment />
        <ContactForm />
      </motion.main>
      <Footer onNavigate={handlePageChange} />
    </div>
  );
}

export default App;
