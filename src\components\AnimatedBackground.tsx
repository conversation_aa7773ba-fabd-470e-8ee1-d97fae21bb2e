import { useEffect, useRef } from 'react';

interface AnimatedBackgroundProps {
  enableParticles?: boolean;
}

const AnimatedBackground = ({ enableParticles = true }: AnimatedBackgroundProps) => {
  const particlesContainerRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // Create floating particles
  useEffect(() => {
    if (!enableParticles) return;

    const createParticles = () => {
      const container = particlesContainerRef.current;
      if (!container) return;

      // Clear existing particles
      container.innerHTML = '';

      // Add a small delay to ensure proper positioning
      setTimeout(() => {
        if (!particlesContainerRef.current) return;

        // Reduce particle count on mobile for performance
        const particleCount = window.innerWidth > 768 ? 60 : 25;

        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'floating-particle';

          // Random horizontal position
          particle.style.left = Math.random() * 100 + '%';

          // Let CSS animation handle positioning - don't set initial top position

          // Create truly random distribution by giving each particle a random starting point
          // in its animation cycle instead of just delaying the start
          const animationDuration = Math.random() * 15 + 15;
          const randomStartDelay = -(Math.random() * animationDuration); // Negative delay = start mid-cycle

          particle.style.animationDelay = randomStartDelay + 's';
          particle.style.animationDuration = animationDuration + 's';

          // Set the final opacity for both initial load and normal spawning
          const finalOpacity = Math.random() * 0.7 + 0.4;
          particle.style.setProperty('--final-opacity', finalOpacity.toString());

          // For initial load, start with opacity 0 and let container animation handle it
          // For normal spawning, set the opacity directly
          const container = particlesContainerRef.current;
          const isInitialLoad = container?.classList.contains('animate-particles-container-fade-in');

          if (isInitialLoad) {
            particle.style.opacity = '0'; // Container animation will handle fade-in
          } else {
            particle.style.opacity = finalOpacity.toString(); // Direct opacity for spawning particles
          }

          // Random size
          const size = Math.random() * 3 + 2;
          particle.style.width = size + 'px';
          particle.style.height = size + 'px';

          particlesContainerRef.current?.appendChild(particle);
        }
      }, 50);
    };

    // Add delay before initial creation to prevent glitches
    const initialTimer = setTimeout(createParticles, 100);

    // Recreate particles on window resize with debouncing and threshold
    let resizeTimeout: number;
    let lastWidth = window.innerWidth;
    let lastHeight = window.innerHeight;
    let isResizing = false;

    const handleResize = () => {
      if (isResizing) return; // Prevent multiple simultaneous resize handlers

      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        // More restrictive threshold for mobile to prevent address bar changes from triggering reset
        const isMobile = currentWidth < 768;
        const widthThreshold = isMobile ? 100 : 50; // Larger threshold on mobile
        const heightThreshold = isMobile ? 150 : 50; // Much larger threshold for height on mobile (address bar)

        const widthChange = Math.abs(currentWidth - lastWidth);
        const heightChange = Math.abs(currentHeight - lastHeight);

        // Only recreate if width changes significantly OR height changes significantly (but not minor mobile browser UI changes)
        if (widthChange > widthThreshold || heightChange > heightThreshold) {
          isResizing = true;
          lastWidth = currentWidth;
          lastHeight = currentHeight;
          createParticles();

          // Reset the flag after particles are created
          setTimeout(() => {
            isResizing = false;
          }, 100);
        }
      }, 500); // Even longer debounce time
    };

    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      clearTimeout(initialTimer);
      clearTimeout(resizeTimeout);
      window.removeEventListener('resize', handleResize);
    };
  }, [enableParticles]);

  // Smooth parallax effect for grid (now enabled on mobile too)
  useEffect(() => {
    // Enable parallax on all devices for testing

    let currentScrollY = 0;
    let targetScrollY = 0;
    let animationId: number;

    const updateParallax = () => {
      if (!gridRef.current) return;

      // Smooth interpolation for consistent movement
      currentScrollY += (targetScrollY - currentScrollY) * 0.1; // Smooth factor

      const parallaxSpeed = 0.2;
      // Move grid in opposite direction (negative) for proper parallax effect
      gridRef.current.style.transform = `translate3d(0, ${-currentScrollY * parallaxSpeed}px, 0)`;

      // Continue animation if there's still movement to catch up
      if (Math.abs(targetScrollY - currentScrollY) > 0.1) {
        animationId = requestAnimationFrame(updateParallax);
      }
    };

    const handleScroll = () => {
      targetScrollY = window.pageYOffset;

      // Cancel previous animation and start new one
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      animationId = requestAnimationFrame(updateParallax);
    };

    // Use passive listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return (
    <>
      {/* Background base layer */}
      <div className="fixed inset-0 z-[-100] bg-gray-900">
        {/* Background texture/pattern with parallax */}
        <div className="absolute inset-0 opacity-[0.4] overflow-hidden">
          <div
            ref={gridRef}
            className="w-full bg-repeat animate-pulse-slow"
            style={{
              backgroundImage: `
                linear-gradient(rgba(124, 58, 237, 0.9) 1.5px, transparent 1.5px),
                linear-gradient(90deg, rgba(124, 58, 237, 0.9) 1.5px, transparent 1.5px)
              `,
              backgroundSize: '50px 50px',
              backgroundPosition: '2px 0, 2px 0',
              height: 'calc(100vh + 300%)', // Much more extended height
              willChange: 'transform, opacity', // Optimize for both transforms and opacity
              top: '-100%', // Start much higher to account for parallax movement
              backfaceVisibility: 'hidden', // Prevent flickering
              perspective: '1000px' // Enable 3D acceleration
            }}
          />
        </div>

        {/* Moving gradient blobs layer - hidden on mobile */}
        <div className="absolute inset-0 overflow-hidden hidden md:block">
          {/* Gradient Sphere 1 - Top Left with smoother blur */}
          <div className="absolute w-[600px] h-[600px] -top-72 -left-72 rounded-full opacity-40 animate-float-1">
            <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-500/80 to-pink-600/80 rounded-full blur-[150px]" />
          </div>

          {/* Gradient Sphere 2 - Bottom Right with smoother blur */}
          <div className="absolute w-[800px] h-[800px] -bottom-96 -right-96 rounded-full opacity-40 animate-float-2">
            <div className="w-full h-full bg-gradient-to-br from-pink-500 to-blue-400 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-pink-500/80 to-blue-400/80 rounded-full blur-[150px]" />
          </div>

          {/* Gradient Sphere 3 - Center with smoother blur */}
          <div className="absolute w-[500px] h-[500px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full opacity-40 animate-float-3">
            <div className="w-full h-full bg-gradient-to-br from-purple-600 to-cyan-400 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-600/80 to-cyan-400/80 rounded-full blur-[150px]" />
          </div>
        </div>

        {/* Floating particles layer */}
        <div
          ref={particlesContainerRef}
          className="absolute inset-0 pointer-events-none overflow-hidden z-[40] animate-particles-container-fade-in"
        />
      </div>
    </>
  );
};

export default AnimatedBackground;
