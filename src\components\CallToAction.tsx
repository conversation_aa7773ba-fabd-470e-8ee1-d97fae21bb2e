import { motion } from "framer-motion";
import { BsCalendar2Check } from "react-icons/bs";
import { Link } from "react-scroll";

const CallToAction = () => {
  return (
    <section className="py-20">

      <div className="container-custom relative z-10">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
          layout
          className="rounded-2xl border border-gray-800 bg-gray-900/20 p-8 backdrop-blur-sm md:p-12 lg:p-16 backdrop-blur-sm"
        >
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
              Ready to <span className="gradient-text">Transform</span> Your
              <br/> Content Production?
            </h2>
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-center md:space-x-6 md:space-y-0">
              <a
                href="https://calendly.com/diftra/intro-call"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary group flex items-center justify-center gap-2 px-8 py-4 text-lg"
              >
                <span>Book a Call</span>
                <motion.span
                  className="inline-block"
                  animate={{ y: [0, -4, 0] }}
                  transition={{
                    duration: 1,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatDelay: 3.5,
                  }}
                >
                  <BsCalendar2Check /> {/* Icon with animation */}
                </motion.span>
              </a>
              <Link
                to="contact"
                spy={true}
                smooth={typeof window !== 'undefined' && window.innerWidth >= 768} // Only smooth on desktop
                offset={-80}
                duration={typeof window !== 'undefined' && window.innerWidth < 768 ? 1200 : 800} // Longer duration for mobile
                className="btn-secondary flex items-center justify-center px-8 py-4 text-lg cursor-pointer"
              >
                Contact Us
              </Link>
            </div>
            <p className="mt-8 text-sm text-gray-400">
              No strings attached. We'll answer your questions and provide a
              clear roadmap for implementation.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CallToAction;
