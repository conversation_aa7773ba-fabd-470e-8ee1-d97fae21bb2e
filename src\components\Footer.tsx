import { motion } from "framer-motion";
import { useCallback } from "react";
import { FiArrowUp } from "react-icons/fi";
import { Link } from "react-scroll";
import Animated<PERSON>ogo from "./AnimatedLogo";
import Logo2Full from "./LOGO Systems.svg?react";

interface FooterProps {
  onNavigate?: (page: 'home' | 'privacy' | 'terms') => void;
}

const Footer = ({ onNavigate }: FooterProps) => {
  const currentYear = new Date().getFullYear();

  // Custom smooth scroll function with better control
  const scrollToTop = useCallback(() => {
    const isMobile = window.innerWidth < 768;
    const duration = isMobile ? 1000 : 800; // Slightly longer duration for mobile
    const startPosition = window.pageYOffset;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsedTime = currentTime - startTime;
      const progress = Math.min(elapsedTime / duration, 1);

      let easeProgress;
      if (isMobile) {
        // Linear/constant speed for mobile
        easeProgress = progress;
      } else {
        // Easing function: easeInOutCubic for desktop
        easeProgress =
          progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - (-2 * progress + 2) ** 3 / 2;
      }

      window.scrollTo(0, startPosition * (1 - easeProgress));

      if (elapsedTime < duration) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }, []);

  return (
    <motion.footer
      layout
      transition={{ duration: 0.6, ease: [0.4, 0.0, 0.2, 1] }}
      className="py-12 relative"
    >
      {/* Gradient background - transition occurs in upper portion, most of footer is opaque */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(to top, rgba(17, 24, 39, 0.8) 0%, rgba(17, 24, 39, 0.6) 35%, rgba(17, 24, 39, 0.4) 65%, rgba(17, 24, 39, 0.3) 80%, transparent 100%)'
        }}
      />

      <div className="container-custom relative z-10">
        <div className="mb-8 flex flex-col items-center justify-between gap-6 border-b border-gray-800 pb-8 md:flex-row">
          <div>
            <div className="text-2xl font-bold">
              <Link
                to="hero"
                spy={true}
                smooth={true}
                offset={-80}
                duration={800}
                className="w-full flex justify-center md:justify-start items-center select-none cursor-pointer"
              >
                <AnimatedLogo
                  svg={Logo2Full}
                  trigger="hover"
                  duration={2000}
                  style={{ height: "2.5rem", width: "auto" }}
                  className="mr-2"
                  strokeColor="#9E7FF7"
                />
              </Link>
            </div>
            <p className="mt-2 text-gray-400">
              Automation solutions for creative agencies
            </p>
          </div>

          <div className="flex flex-wrap justify-center md:justify-start gap-x-6 gap-y-2">
            <FooterLink to="hero" label="Home" />
            <FooterLink to="services" label="Services" />
            <FooterLink to="why-choose-us" label="Why Us" />
            <FooterLink to="process" label="Process" />
            <FooterLink to="why-now" label="Why Now" />
            <FooterLink to="faq" label="FAQ" />
            <FooterLink to="contact" label="Contact" />
          </div>

          <button
            onClick={scrollToTop}
            className="group flex items-center justify-center rounded-full border border-gray-800 bg-gray-900 p-3 transition-all duration-300 hover:bg-gray-800 hover:shadow-lg hover:shadow-purple-500/10"
            aria-label="Scroll to top"
          >
            <FiArrowUp className="h-5 w-5 text-white transition-transform duration-300 group-hover:-translate-y-1" />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center text-sm text-gray-500 space-y-2">
          <div className="flex flex-wrap items-center justify-center gap-x-4 gap-y-1">
            <span>&copy; {currentYear} Diftra. All rights reserved.</span>
            {onNavigate && (
              <>
                <span className="hidden sm:inline">•</span>
                <button
                  onClick={() => onNavigate('privacy')}
                  className="text-gray-500 hover:text-purple-400 transition-colors underline"
                >
                  Privacy Policy
                </button>
                <span>•</span>
                <button
                  onClick={() => onNavigate('terms')}
                  className="text-gray-500 hover:text-purple-400 transition-colors underline"
                >
                  Terms & Conditions
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

interface FooterLinkProps {
  to: string;
  label: string;
}

const FooterLink = ({ to, label }: FooterLinkProps) => {
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  return (
    <Link
      to={to}
      spy={true}
      smooth={!isMobile} // Disable smooth scrolling on mobile for constant speed
      offset={-80}
      duration={isMobile ? 1200 : 1000} // Slightly longer duration for mobile
      className="cursor-pointer font-medium text-gray-400 transition-colors hover:text-purple-400"
    >
      {label}
    </Link>
  );
};

export default Footer;
