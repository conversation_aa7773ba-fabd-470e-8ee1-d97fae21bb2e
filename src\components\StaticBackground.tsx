const StaticBackground = () => {
  return (
    <>
      {/* Background base layer */}
      <div className="fixed inset-0 z-[-100] bg-gray-900">
        {/* Background texture/pattern - static version for legal pages */}
        <div className="absolute inset-0 opacity-[0.4]">
          <div
            className="w-full h-full bg-repeat animate-pulse-slow"
            style={{
              backgroundImage: `
                linear-gradient(rgba(124, 58, 237, 0.9) 1.5px, transparent 1.5px),
                linear-gradient(90deg, rgba(124, 58, 237, 0.9) 1.5px, transparent 1.5px)
              `,
              backgroundSize: '50px 50px',
              backgroundPosition: '2px 0, 2px 0'
            }}
          />
        </div>

        {/* Moving gradient blobs layer - hidden on mobile */}
        <div className="absolute inset-0 overflow-hidden hidden md:block">
          {/* Gradient Sphere 1 - Top Left with smoother blur */}
          <div className="absolute w-[600px] h-[600px] -top-72 -left-72 rounded-full opacity-40 animate-float-1">
            <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-500/80 to-pink-600/80 rounded-full blur-[150px]" />
          </div>

          {/* Gradient Sphere 2 - Bottom Right with smoother blur */}
          <div className="absolute w-[800px] h-[800px] -bottom-96 -right-96 rounded-full opacity-40 animate-float-2">
            <div className="w-full h-full bg-gradient-to-br from-pink-500 to-blue-400 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-pink-500/80 to-blue-400/80 rounded-full blur-[150px]" />
          </div>

          {/* Gradient Sphere 3 - Center with smoother blur */}
          <div className="absolute w-[500px] h-[500px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full opacity-40 animate-float-3">
            <div className="w-full h-full bg-gradient-to-br from-purple-600 to-cyan-400 rounded-full blur-[120px]" />
            <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-600/80 to-cyan-400/80 rounded-full blur-[150px]" />
          </div>
        </div>

        {/* No particles layer for legal pages */}
      </div>
    </>
  );
};

export default StaticBackground;
