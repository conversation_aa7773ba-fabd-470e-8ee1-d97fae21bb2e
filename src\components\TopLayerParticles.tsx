import { useEffect, useRef } from 'react';

interface TopLayerParticlesProps {
  enabled?: boolean;
}

const TopLayerParticles = ({ enabled = true }: TopLayerParticlesProps) => {
  const particlesContainerRef = useRef<HTMLDivElement>(null);

  // Create floating particles for the top layer
  useEffect(() => {
    if (!enabled) return;

    const createParticles = () => {
      const container = particlesContainerRef.current;
      if (!container) return;

      // Clear existing particles
      container.innerHTML = '';

      // Add a small delay to ensure proper positioning
      setTimeout(() => {
        if (!particlesContainerRef.current) return;

        // Fewer particles for the top layer to avoid overwhelming the UI
        const particleCount = window.innerWidth > 768 ? 26 : 10;

        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'top-layer-particle';

          // Random horizontal position
          particle.style.left = Math.random() * 100 + '%';

          // Let CSS animation handle positioning - don't set initial top position

          // Create truly random distribution by giving each particle a random starting point
          // in its animation cycle instead of just delaying the start
          const animationDuration = Math.random() * 12 + 12;
          const randomStartDelay = -(Math.random() * animationDuration); // Negative delay = start mid-cycle

          particle.style.animationDelay = randomStartDelay + 's';
          particle.style.animationDuration = animationDuration + 's';

          // Set the final opacity for both initial load and normal spawning
          const finalOpacity = Math.random() * 0.5 + 0.3;
          particle.style.setProperty('--final-opacity', finalOpacity.toString());

          // For initial load, start with opacity 0 and let container animation handle it
          // For normal spawning, set the opacity directly
          const container = particlesContainerRef.current;
          const isInitialLoad = container?.classList.contains('animate-particles-container-fade-in');

          if (isInitialLoad) {
            particle.style.opacity = '0'; // Container animation will handle fade-in
          } else {
            particle.style.opacity = finalOpacity.toString(); // Direct opacity for spawning particles
          }

          // Smaller size than background particles
          const size = Math.random() * 2.6 + 1.3;
          particle.style.width = size + 'px';
          particle.style.height = size + 'px';

          particlesContainerRef.current?.appendChild(particle);
        }
      }, 75); // Slightly different delay than background particles
    };

    // Add delay before initial creation to prevent glitches
    const initialTimer = setTimeout(createParticles, 150);

    // Recreate particles on window resize with debouncing and threshold
    let resizeTimeout: number;
    let lastWidth = window.innerWidth;
    let lastHeight = window.innerHeight;
    let isResizing = false;

    const handleResize = () => {
      if (isResizing) return; // Prevent multiple simultaneous resize handlers

      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        // More restrictive threshold for mobile to prevent address bar changes from triggering reset
        const isMobile = currentWidth < 768;
        const widthThreshold = isMobile ? 100 : 50; // Larger threshold on mobile
        const heightThreshold = isMobile ? 150 : 50; // Much larger threshold for height on mobile (address bar)

        const widthChange = Math.abs(currentWidth - lastWidth);
        const heightChange = Math.abs(currentHeight - lastHeight);

        // Only recreate if width changes significantly OR height changes significantly (but not minor mobile browser UI changes)
        if (widthChange > widthThreshold || heightChange > heightThreshold) {
          isResizing = true;
          lastWidth = currentWidth;
          lastHeight = currentHeight;
          createParticles();

          // Reset the flag after particles are created
          setTimeout(() => {
            isResizing = false;
          }, 100);
        }
      }, 550); // Even longer debounce time
    };

    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      clearTimeout(initialTimer);
      clearTimeout(resizeTimeout);
      window.removeEventListener('resize', handleResize);
    };
  }, [enabled]);

  if (!enabled) {
    return null;
  }

  return (
    <div
      ref={particlesContainerRef}
      className="fixed inset-0 pointer-events-none overflow-hidden z-[9999] animate-particles-container-fade-in"
    />
  );
};

export default TopLayerParticles;
