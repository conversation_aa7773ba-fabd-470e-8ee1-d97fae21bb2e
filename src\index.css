@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-900 font-sans text-white;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

@layer components {
  .container-custom {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .btn-primary {
    @apply bg-purple-600 px-6 py-3 font-semibold text-white transition duration-300 hover:bg-purple-700 rounded-md;
  }

  .btn-secondary {
    @apply border border-purple-600 px-6 py-3 font-semibold text-white transition duration-300 hover:bg-purple-600/20 rounded-md;
  }

  .btn-outline-gradient {
    position: relative;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: #e2e8f0; /* text-purple-100 */
    border-radius: 0.375rem;
    background-color: transparent;
    overflow: hidden;
    box-shadow: none;
    transform: translateY(0);
    transition: color 300ms, transform 300ms, filter 300ms, background-color 300ms;
  }

  .btn-outline-gradient::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 0.375rem;
    z-index: -10;
    background: linear-gradient(to right, #a78bfa, #db2777);
    padding: 2px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    transition: background 500ms;
  }

  .btn-outline-gradient:hover {
    background-color: transparent;
    color: white;
    transform: translateY(-2px);
    filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.4));
  }

  .btn-outline-gradient:hover::before {
    background: linear-gradient(to right, #c4b5fd, #ec4899);
  }

  /* Override transform for nav bar CTA button */
  .nav-cta-button:hover {
    transform: translateY(0) !important;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .section-title {
    @apply mb-12 text-center text-3xl font-bold md:text-4xl lg:text-5xl;
  }

  .section-subtitle {
    @apply mb-16 text-center text-lg text-gray-400;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent;
  }
}

/* Custom cursor styling */
.custom-cursor {
  @apply pointer-events-none fixed z-50 h-8 w-8 -translate-x-1/2 -translate-y-1/2 rounded-full border-2 border-purple-500 transition-transform duration-200 ease-out;
  mix-blend-mode: difference;
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.float {
  animation: float 4s ease-in-out infinite;
}

/* FAQ accordion styling */
/* FAQ Section Styling */
.faq-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.faq-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

.section-header {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
  transition-delay: 0.1s;
}

.faq-section.is-visible .section-header {
  opacity: 1;
  transform: translateY(0);
}

/* FAQ Item Styling */
.faq-item {
  @apply border-b border-gray-800 py-6;
  opacity: 0;
  transform: translateY(15px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Remove border from last FAQ item */
.faq-item-wrapper:last-child .faq-item {
  border-bottom: none;
}

.faq-item.is-visible {
  opacity: 1;
  transform: translateY(0);
}

.faq-item-wrapper {
  transition-delay: calc(var(--index, 0) * 100ms);
}

.faq-item-wrapper:nth-child(1) .faq-item.is-visible { transition-delay: 0.1s; }
.faq-item-wrapper:nth-child(2) .faq-item.is-visible { transition-delay: 0.15s; }
.faq-item-wrapper:nth-child(3) .faq-item.is-visible { transition-delay: 0.2s; }
.faq-item-wrapper:nth-child(4) .faq-item.is-visible { transition-delay: 0.25s; }
.faq-item-wrapper:nth-child(5) .faq-item.is-visible { transition-delay: 0.3s; }
.faq-item-wrapper:nth-child(6) .faq-item.is-visible { transition-delay: 0.35s; }

.faq-question {
  @apply flex cursor-pointer items-center justify-between text-xl font-medium;
  padding: 0.5rem 0;
}

/* FAQ Icon Animation - More dynamic style */
.faq-icon {
  @apply text-purple-400;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform;
  transform-origin: center;
}

.faq-icon.open {
  transform: rotate(135deg);
}

/* WorkflowLoop-style FAQ Content Animation - DOM-oriented approach */
.faq-content {
  overflow: hidden;
  height: 0;
  transition: height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: height;
  position: relative;
}

.faq-content.open {
  visibility: visible;
}

/* All FAQ content expands downward */

.faq-answer {
  @apply text-gray-400 py-4;
  width: 100%; /* Ensure full width */
  word-wrap: break-word; /* Ensure text wraps properly */
}

/* Self-assessment slider styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: rgba(124, 58, 237, 0.2);
  border-radius: 999px;
  height: 6px;
  transition: all 0.3s ease;
}

input[type="range"]:hover {
  background: rgba(124, 58, 237, 0.4);
  height: 8px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #a78bfa;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(167, 139, 250, 0.5);
  transition: all 0.3s ease;
}

input[type="range"]:hover::-webkit-slider-thumb {
  width: 20px;
  height: 20px;
  background: #9333ea;
  box-shadow: 0 0 10px rgba(167, 139, 250, 0.7);
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #a78bfa;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 5px rgba(167, 139, 250, 0.5);
  transition: all 0.3s ease;
}

input[type="range"]:hover::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #9333ea;
  box-shadow: 0 0 10px rgba(167, 139, 250, 0.7);
}

input[type="range"]:focus {
  outline: none;
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 5.9% 10%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

/* Send button icon animation */
.send-icon {
  transition: all 0.3s ease;
}

button:hover .send-icon {
  transform: translateX(4px) rotate(40deg);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Smoother animations on mobile */
  .motion-safe\:animate-fade-in {
    animation-duration: 0.7s;
  }

  /* Prevent animation glitches on mobile */
  .motion-reduce\:transform-none {
    transform: none !important;
    transition: opacity 0.5s ease-out !important;
  }

  /* FAQ optimizations for mobile */
  .faq-item {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .faq-question {
    align-items: flex-start;
  }

  .faq-question h3 {
    font-size: 1rem;
    line-height: 1.5;
    padding-right: 1rem;
  }

  .faq-answer {
    will-change: height, opacity;
    transform: translateZ(0); /* Force GPU acceleration */
  }

  .answer-content {
    padding-bottom: 0.1px; /* Prevent margin collapse */
  }

  .faq-answer p {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  /* Self-Assessment optimizations for mobile */
  input[type="range"] {
    width: 100%;
    max-width: 140px;
  }

  input[type="range"]::-webkit-slider-thumb {
    width: 14px;
    height: 14px;
  }

  input[type="range"]::-moz-range-thumb {
    width: 14px;
    height: 14px;
  }
}

/* Services animations */
.services-animate,
.services-card {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  will-change: opacity, transform;
}

/* Service card hover effects */
.services-card {
  border: 1px solid rgb(31, 41, 55); /* border-gray-800 to match end-to-end solutions section */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, border-color, box-shadow;
}

.services-card:hover {
  border-color: rgba(147, 51, 234, 0.5); /* Original purple border on hover */
  box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1), 0 8px 10px -6px rgba(124, 58, 237, 0.1);
  transform: translateY(-2px);
}

/* iOS-specific optimizations */
@supports (-webkit-touch-callout: none) {
  /* Force hardware acceleration for animations on iOS */
  .faq-answer,
  .faq-content,
  .faq-icon,
  .fade-in-section,
  .services-animate,
  .services-card {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
    will-change: opacity, transform;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }

  /* Optimize FAQ animations on iOS */
  .faq-section {
    -webkit-transition: opacity 0.8s ease, -webkit-transform 0.8s ease;
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .section-header {
    -webkit-transition: opacity 0.6s ease, -webkit-transform 0.6s ease;
    transition: opacity 0.6s ease, transform 0.6s ease;
    -webkit-transition-delay: 0.2s;
    transition-delay: 0.2s;
  }

  .faq-item {
    -webkit-transition: opacity 0.5s ease, -webkit-transform 0.5s ease;
    transition: opacity 0.5s ease, transform 0.5s ease;
  }

  .services-card:hover {
    border-color: rgba(147, 51, 234, 0.5) !important; /* Original purple border on hover */
    box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1) !important;
    transform: translateY(-2px) !important;
  }

  /* WorkflowLoop-style iOS-specific FAQ content animations - DOM approach */
  .faq-content {
    -webkit-transition: height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    transition: height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    will-change: height;
  }

  .faq-icon {
    -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: center;
  }

  /* Optimize mobile animations specifically for iOS */
  @media (max-width: 768px) {
    .faq-content {
      /* Use same animations on iOS mobile for consistency */
      -webkit-transition: height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
      transition: height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
      will-change: height;
    }
  }

  /* Optimize animations on iOS */
  .services-card {
    -webkit-transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, border-color, box-shadow;
  }

  /* Customize hover effects on iOS */
  .services-card {
    border-color: rgb(31, 41, 55) !important; /* border-gray-800 to match end-to-end solutions section */
  }

  .services-card:hover {
    border-color: rgba(147, 51, 234, 0.5) !important; /* Original purple border on hover */
    box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1) !important;
    transform: translateY(-2px) !important;
  }

  /* Optimize mobile animations on iOS */
  @media (max-width: 768px) {
    .services-card,
    .services-animate {
      -webkit-transition: opacity 0.6s ease, -webkit-transform 0.6s ease;
      transition: opacity 0.6s ease, transform 0.6s ease;
    }

    /* Override hover transition for mobile to match desktop smoothness */
    .services-card {
      border-color: rgb(31, 41, 55) !important; /* border-gray-800 to match end-to-end solutions section */
      -webkit-transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    }

    .services-card:hover,
    .services-card:active {
      border-color: rgba(147, 51, 234, 0.5) !important; /* Original purple border on hover/touch */
      box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1), 0 8px 10px -6px rgba(124, 58, 237, 0.1) !important;
      transform: translateY(-2px) !important;
    }
  }
}

/* Card hover animation effect - can be applied to any card-like element */
.card-hover-effect {
  border: 1px solid rgb(31, 41, 55); /* border-gray-800 */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, border-color, box-shadow;
}

.card-hover-effect:hover {
  border-color: rgba(147, 51, 234, 0.5); /* Purple border on hover */
  box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1), 0 8px 10px -6px rgba(124, 58, 237, 0.1);
  /* Removed the transform: translateY(-2px); as requested */
}

/* Specific overrides for Real Results and End-to-End Solutions cards */
.results-card-hover-effect,
.solutions-card-hover-effect {
  border: 1px solid rgb(55, 65, 81) !important; /* border-gray-700 */
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, border-color, box-shadow;
}

.results-card-hover-effect:hover,
.solutions-card-hover-effect:hover {
  border-color: rgba(147, 51, 234, 0.5) !important; /* Purple border on hover */
  box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1), 0 8px 10px -6px rgba(124, 58, 237, 0.1);
}



/* Hamburger Menu Icon Animation */
.hamburger-icon {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
}

.hamburger-line {
  display: block;
  width: 100%;
  height: 2px;
  background-color: #e2e8f0; /* text-gray-300 */
  border-radius: 2px;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
              opacity 0.3s ease;
  transform-origin: center;
  will-change: transform, opacity;
}

/* Animation for the top line */
.hamburger-icon.open .hamburger-line:nth-child(1) {
  transform: translateY(11px) rotate(45deg);
}

/* Animation for the middle line */
.hamburger-icon.open .hamburger-line:nth-child(2) {
  opacity: 0;
}

/* Animation for the bottom line */
.hamburger-icon.open .hamburger-line:nth-child(3) {
  transform: translateY(-11px) rotate(-45deg);
}

/* Animated Background Styles */
/* Enhanced gradient blob floating animations */
@keyframes float-1 {
  0%, 100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(120px, -100px) scale(1.15) rotate(90deg);
  }
  50% {
    transform: translate(-80px, 120px) scale(0.85) rotate(180deg);
  }
  75% {
    transform: translate(-120px, -60px) scale(1.1) rotate(270deg);
  }
}

@keyframes float-2 {
  0%, 100% {
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(-140px, 80px) scale(0.9) rotate(-90deg);
  }
  50% {
    transform: translate(100px, -140px) scale(1.2) rotate(-180deg);
  }
  75% {
    transform: translate(140px, 80px) scale(0.9) rotate(-270deg);
  }
}

@keyframes float-3 {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(-40%, -60%) scale(1.1) rotate(90deg);
  }
  50% {
    transform: translate(-60%, -40%) scale(0.9) rotate(180deg);
  }
  75% {
    transform: translate(-45%, -55%) scale(1.05) rotate(270deg);
  }
}

/* Floating particles animation */
@keyframes particle-float {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

/* Top layer particles animation - slightly different movement pattern */
@keyframes top-particle-float {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    transform: translateY(-120px) translateX(-30px) rotate(-360deg);
    opacity: 0;
  }
}

/* Particle fade-in animation */
@keyframes particle-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Container fade-in animation for initial page load */
@keyframes particles-container-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Individual particle fade-in that respects the final opacity */
@keyframes particle-fade-in-custom {
  0% {
    opacity: 0;
  }
  100% {
    opacity: var(--final-opacity, 1);
  }
}

/* Pulse animation for background pattern */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

/* Apply animations */
.animate-float-1 {
  animation: float-1 18s infinite ease-in-out;
  will-change: transform;
}

.animate-float-2 {
  animation: float-2 22s infinite ease-in-out;
  animation-delay: 6s;
  will-change: transform;
}

.animate-float-3 {
  animation: float-3 15s infinite ease-in-out;
  animation-delay: 10s;
  will-change: transform;
}

.animate-pulse-slow {
  animation: pulse-slow 6s infinite ease-in-out;
  will-change: opacity;
}

.animate-particles-container-fade-in {
  animation: particles-container-fade-in 2.5s ease-out;
  opacity: 0;
  animation-fill-mode: forwards;
}

/* Animate individual particles to their final opacity when container fades in */
.animate-particles-container-fade-in .floating-particle,
.animate-particles-container-fade-in .top-layer-particle {
  animation: particle-float linear infinite, particle-fade-in-custom 2.5s ease-out;
  animation-fill-mode: forwards, forwards;
}

.floating-particle {
  position: absolute;
  background: #8b5cf6;
  border-radius: 50%;
  pointer-events: none;
  animation: particle-float linear infinite, particle-fade-in 2.5s ease-out;
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.8), 0 0 25px rgba(139, 92, 246, 0.4);
  will-change: transform, opacity;
  opacity: 0; /* Start invisible for fade-in effect */
}

.top-layer-particle {
  position: absolute;
  background: #c084fc;
  border-radius: 50%;
  pointer-events: none;
  animation: top-particle-float linear infinite, particle-fade-in 3s ease-out;
  box-shadow: 0 0 12px rgba(192, 132, 252, 0.7), 0 0 20px rgba(192, 132, 252, 0.3);
  will-change: transform, opacity;
  opacity: 0; /* Start invisible for fade-in effect */
}

/* Optimize animations for mobile */
@media (max-width: 768px) {
  .animate-float-1,
  .animate-float-2,
  .animate-float-3 {
    animation-duration: 25s;
  }

  .floating-particle {
    animation-duration: 20s !important;
  }

  .top-layer-particle {
    animation-duration: 18s !important;
  }

  .animate-pulse-slow {
    animation-duration: 10s; /* Slower animation for better mobile performance */
    animation-timing-function: linear; /* Linear instead of ease-in-out for smoother performance */
  }
}
